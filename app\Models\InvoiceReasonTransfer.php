<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InvoiceReasonTransfer extends Model
{
    use HasFactory;

    protected $fillable = [
        'invoice_reason_id',
        'regular_invoice_id',
        'total_amount_transferred',
        'reason',
        'transferred_at'
    ];

    protected $casts = [
        'transferred_at' => 'datetime',
        'total_amount_transferred' => 'decimal:2'
    ];

    public function invoiceReason()
    {
        return $this->belongsTo(InvoiceReason::class);
    }

    public function regularInvoice()
    {
        return $this->belongsTo(Invoice::class, 'regular_invoice_id');
    }
}
