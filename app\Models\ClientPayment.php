<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ClientPayment extends Model
{
    use HasFactory;

    protected $fillable = [
        'client_id', 'customer_id', 'invoice_id', 'invoice_month', 'payment_status', 'amount_due', 'partial_payment', 'remaining_amount', 'payment_date', 'additional_paid','active_bonus', 'additional_amount_reason',
    ];

    protected $casts = [
        'partial_payment' => 'float',
        'remaining_amount' => 'float',
        'payment_date' => 'datetime', // Ensure this is set to 'datetime'

    ];
    public function paymentHistories()
    {
        return $this->hasMany(ClientPaymentHistory::class, 'client_payment_id');
    }

    public function client()
    {
        return $this->belongsTo(Client::class);
    }

    public function invoice()
    {
        return $this->belongsTo(Invoice::class);
    }
    public function customer()
    {
        return $this->belongsTo(Customer::class); // Adjust the model name if needed
    }
}
