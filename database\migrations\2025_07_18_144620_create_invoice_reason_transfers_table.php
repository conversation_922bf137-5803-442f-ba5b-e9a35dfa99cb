<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invoice_reason_transfers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('invoice_reason_id')->constrained('invoice_reasons')->onDelete('cascade');
            $table->foreignId('regular_invoice_id')->constrained('invoices')->onDelete('cascade');
            $table->decimal('total_amount_transferred', 10, 2);
            $table->text('reason');
            $table->timestamp('transferred_at');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoice_reason_transfers');
    }
};
