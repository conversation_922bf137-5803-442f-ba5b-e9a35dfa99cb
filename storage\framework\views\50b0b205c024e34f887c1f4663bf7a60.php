<?php $__env->startSection('css'); ?>
    <style>
        @media print {
            /* إجبار الجدول على عدم الانقسام بين الصفحات */
            .table-responsive {
                display: block !important;
                width: 100% !important;
                overflow-x: auto !important;
                -webkit-print-color-adjust: exact !important;
            }

            .table {
                width: 100% !important;
                page-break-inside: auto !important;
                page-break-before: auto !important;
                page-break-after: auto !important;
            }

            /* منع تقسيم الصفوف بين الصفحات */
            tr {
                page-break-inside: avoid !important;
                page-break-before: auto !important;
                page-break-after: auto !important;
            }

            /* جعل الصفحة مطولة ومنع التكرار على صفحات متعددة */
            body {
                height: auto !important;
                overflow: visible !important;
            }

            /* إخفاء العناصر غير الضرورية أثناء الطباعة */
            .d-print-none {
                display: none !important;
            }

            /* إخفاء المعلومات الإضافية عند الطباعة */
            .no-print {
                display: none !important;
            }

            /* إخفاء معلومات الآبار والإحصائيات */
            .wells-info-section {
                display: none !important;
            }

            /* إخفاء المبلغ المستحق الكلي من الأعلى */
            .total-amount-summary {
                display: none !important;
            }

            /* إخفاء الأزرار والإجراءات */
            .action-buttons {
                display: none !important;
            }

            /* تقليل الهوامش */
            /*@page {*/
                margin: 5mm !important; /* تقليل الهوامش لاستيعاب محتوى أكبر
            }

            /* جعل الكارد يظهر بشكل كامل دون تقسيم */
            .card {
                page-break-inside: avoid !important;
            }

            /* تقليل حجم الخطوط لاستيعاب 56 عميل */
            .table th, .table td {
                font-size: 10px !important; /* تقليل حجم الخط */
                padding: 4px !important; /* تقليل المسافات الداخلية */
            }

            /* جعل الجدول يظهر بشكل أفقي أفضل */
            .table-responsive {
                white-space: nowrap !important; /* منع التفاف النصوص */
            }
        }
    </style>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <?php $__env->startComponent('components.breadcrumb'); ?>
        <?php $__env->slot('li_1'); ?>
            <?php echo e(__('showInvoice.management')); ?>

        <?php $__env->endSlot(); ?>
        <?php $__env->slot('title'); ?>
            <?php echo e(__('showInvoice.invoice_details')); ?>

        <?php $__env->endSlot(); ?>
    <?php echo $__env->renderComponent(); ?>

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-body">
                    <!-- Success and Error Messages -->
                    <?php if(session('success')): ?>
                        <div class="alert alert-success"><?php echo e(session('success')); ?></div>
                    <?php elseif(session('error')): ?>
                        <div class="alert alert-danger"><?php echo e(session('error')); ?></div>
                    <?php endif; ?>

                    <!-- Invoice Summary -->
                    <div class="invoice-summary mb-4 text-center">
                        <h4 class="card-title mb-3"><?php echo e(__('showInvoice.invoice_summary')); ?></h4>
                        <div class="row">
                            <div class="col-md-6 total-amount-summary">
                                <p class="fs-5">
                                    <strong><?php echo e(__('showInvoice.total_amount_due')); ?></strong>
                                    <?php
                                        $totalAmountArabic = app()->getLocale() == 'ae' ?
                                            convertToArabicNumbers(number_format($invoice->total_amount_due, 2)) :
                                            number_format($invoice->total_amount_due, 2);
                                    ?>
                                    <?php echo e($totalAmountArabic); ?> <?php echo e(__('showInvoice.currency')); ?>

                                </p>
                            </div>
                            <div class="col-md-6">
                                <?php
                                    // Parse the start and end dates from the invoice
                                    $formattedStartDate = \Carbon\Carbon::parse($invoice->start_date);
                                    $formattedEndDate = \Carbon\Carbon::parse($invoice->end_date);

                                    // Check the current locale
                                    if (app()->getLocale() == 'ae') {
                                        // Arabic locale: Format the dates with Arabic numerals and Arabic text
                                        $formattedStartDate = $formattedStartDate->locale('ar')->translatedFormat('d F Y');
                                        $formattedEndDate = $formattedEndDate->locale('ar')->translatedFormat('d F Y');
                                        $separator = 'إلى'; // Arabic 'to'

                                        // Convert numbers to Arabic script
                                        $formattedStartDate = preg_replace_callback('/\d/', function ($matches) {
                                            return convertToArabicNumbers($matches[0]);
                                        }, $formattedStartDate);

                                        $formattedEndDate = preg_replace_callback('/\d/', function ($matches) {
                                            return convertToArabicNumbers($matches[0]);
                                        }, $formattedEndDate);

                                    } else {
                                        // English locale: Format the dates in standard English
                                        $formattedStartDate = $formattedStartDate->format('F d, Y');
                                        $formattedEndDate = $formattedEndDate->format('F d, Y');
                                        $separator = 'to'; // English 'to'
                                    }
                                ?>

                                <p class="fs-5">
                                    <strong><?php echo e(__('showInvoice.period')); ?></strong>
                                    <?php echo e($formattedStartDate); ?> <?php echo e($separator); ?> <?php echo e($formattedEndDate); ?>

                                </p>
                            </div>
                            <div class="col-md-6">
                                <p class="fs-5"><strong><?php echo e(__('showInvoice.reason')); ?></strong> <?php echo e($invoice->reason); ?></p>
                            </div>
                        </div>
                    </div>

                    <!-- Wells Information -->
                    <?php if(!empty($wellsInfo)): ?>
                    <div class="card mt-4 wells-info-section">
                        <div class="card-header">
                            <h5 class="card-title mb-0">معلومات الآبار وأسعار الساعات</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover align-middle table-bordered mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>اسم البئر</th>
                                            <th>إجمالي الساعات</th>
                                            <th>النسبة المئوية</th>
                                            <th>المبلغ المخصص</th>
                                            <th>سعر الساعة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__currentLoopData = $wellsInfo; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $wellInfo): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td><strong><?php echo e($wellInfo['name']); ?></strong></td>
                                            <td><?php echo e(number_format($wellInfo['total_hours'], 2)); ?> ساعة</td>
                                            <td><?php echo e(number_format($wellInfo['percentage'], 2)); ?>%</td>
                                            <td><?php echo e(number_format($wellInfo['allocated_amount'], 2)); ?> جنيه</td>
                                            <td><strong><?php echo e(number_format($wellInfo['hourly_rate'], 2)); ?> جنيه/ساعة</strong></td>
                                        </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Transferred Amounts Section -->
                    <?php if(!empty($transfers) && $transfers->count() > 0): ?>
                    <div class="card mt-4 no-print">
                        <div class="card-header">
                            <h5 class="card-title mb-0">المبالغ المرسلة للفواتير العادية</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover align-middle table-bordered mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>الفاتورة العادية</th>
                                            <th>المبلغ المرسل</th>
                                            <th>تاريخ الإرسال</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__currentLoopData = $transfers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $transfer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td>
                                                <strong>فاتورة شهر <?php echo e(\Carbon\Carbon::parse($transfer->regularInvoice->month)->locale('ar')->translatedFormat('F Y')); ?></strong>
                                            </td>
                                            <td>
                                                <?php
                                                    $transferAmountArabic = app()->getLocale() == 'ae' ?
                                                        convertToArabicNumbers(number_format($transfer->total_amount_transferred, 2)) :
                                                        number_format($transfer->total_amount_transferred, 2);
                                                ?>
                                                <span class="badge bg-success"><?php echo e($transferAmountArabic); ?> جنيه</span>
                                            </td>
                                            <td>
                                                <?php echo e($transfer->transferred_at->locale('ar')->translatedFormat('d F Y - H:i')); ?>

                                            </td>
                                            <td>
                                                <form action="<?php echo e(route('invoice-reason.remove-from-regular-invoice', ['invoiceReasonId' => $invoice->id, 'transferId' => $transfer->id])); ?>" method="POST" style="display: inline;">
                                                    <?php echo csrf_field(); ?>
                                                    <?php echo method_field('DELETE'); ?>
                                                    <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذا التحويل؟')">
                                                        <i class="mdi mdi-delete"></i> حذف
                                                    </button>
                                                </form>
                                            </td>
                                        </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Client Breakdown Table -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0"><?php echo e(__('showInvoice.client_breakdown')); ?></h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover align-middle table-bordered mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th width="8%">المعرف</th>
                                            <th width="20%">اسم العميل/المشارك</th>
                                            <th width="10%">النوع</th>
                                            <th width="12%">الساعات</th>
                                            <th width="15%">تفاصيل الآبار</th>
                                            <th width="15%">المبلغ المستحق</th>
                                            <th width="15%">إجمالي العميل</th>
                                            <th width="10%">حالة الدفع</th>
                                            <th width="10%">المتبقي</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__currentLoopData = $clientData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            
                                            <tr>
                                                <td class="text-center fw-bold"><?php echo e($data['client_id']); ?></td>
                                                <td>
                                                    <strong><?php echo e($data['client']); ?></strong>
                                                    <br><small class="text-muted">العميل الأساسي</small>
                                                </td>
                                                <td class="text-center">
                                                    <span class="badge bg-secondary">أساسي</span>
                                                </td>
                                                <td class="text-center">
                                                    <?php
                                                        $clientHours = $data['client_hours'];
                                                        $totalHours = floor($clientHours);
                                                        $minutes = round(($clientHours - $totalHours) * 60);
                                                        if ($minutes >= 60) {
                                                            $totalHours += floor($minutes / 60);
                                                            $minutes = $minutes % 60;
                                                        }
                                                        $formattedHours = convertToArabicNumbers($totalHours);
                                                        $formattedMinutes = convertToArabicNumbers($minutes);
                                                    ?>
                                                    <span class="fw-bold"><?php echo e($formattedHours); ?> ساعة و <?php echo e($formattedMinutes); ?> دقيقة</span>
                                                </td>
                                                <td class="text-center">
                                                    <?php if(!empty($data['client_wells'])): ?>
                                                        <?php $__currentLoopData = $data['client_wells']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $well): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <div class="mb-1">
                                                                <strong><?php echo e($well['name']); ?>:</strong>
                                                                <?php
                                                                    $wellHours = $well['hours'];
                                                                    $hours = floor($wellHours);
                                                                    $minutes = round(($wellHours - $hours) * 60);
                                                                    if ($minutes >= 60) {
                                                                        $hours += floor($minutes / 60);
                                                                        $minutes = $minutes % 60;
                                                                    }
                                                                    $formattedHours = convertToArabicNumbers($hours);
                                                                    $formattedMinutes = convertToArabicNumbers($minutes);
                                                                ?>
                                                                <?php echo e($formattedHours); ?> ساعة و <?php echo e($formattedMinutes); ?> دقيقة
                                                                <?php
                                                                    $hourlyRateArabic = app()->getLocale() == 'ae' ?
                                                                        convertToArabicNumbers(number_format($well['hourly_rate'], 2)) :
                                                                        number_format($well['hourly_rate'], 2);
                                                                ?>
                                                                <br><small class="text-muted">(<?php echo e($hourlyRateArabic); ?> جنيه/ساعة)</small>
                                                            </div>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    <?php endif; ?>
                                                </td>
                                                <td class="text-center">
                                                    <?php
                                                        $clientAmountArabic = app()->getLocale() == 'ae' ?
                                                            convertToArabicNumbers(number_format($data['client_amount'], 2)) :
                                                            number_format($data['client_amount'], 2);
                                                    ?>
                                                    <span class="fw-bold"><?php echo e($clientAmountArabic); ?></span>
                                                    <br><small class="text-muted">جنيه</small>
                                                </td>
                                                <td class="text-center" rowspan="<?php echo e(count($data['customer_details']) + 1); ?>">
                                                    <div class="border rounded p-2" style="background-color: #f8f9fa;">
                                                        <?php
                                                            $totalAmountArabic = app()->getLocale() == 'ae' ?
                                                                convertToArabicNumbers(number_format($data['total_amount'], 2)) :
                                                                number_format($data['total_amount'], 2);
                                                        ?>
                                                        <strong class="fs-5"><?php echo e($totalAmountArabic); ?></strong>
                                                        <br><small class="text-muted">جنيه</small>
                                                        <hr class="my-1">
                                                        <?php
                                                            $totalHours = $data['total_hours'];
                                                            $hours = floor($totalHours);
                                                            $minutes = round(($totalHours - $hours) * 60);
                                                            if ($minutes >= 60) {
                                                                $hours += floor($minutes / 60);
                                                                $minutes = $minutes % 60;
                                                            }
                                                            $formattedHours = convertToArabicNumbers($hours);
                                                            $formattedMinutes = convertToArabicNumbers($minutes);
                                                        ?>
                                                        <small class="text-muted"><?php echo e($formattedHours); ?> ساعة و <?php echo e($formattedMinutes); ?> دقيقة</small>
                                                    </div>
                                                </td>
                                                <td class="text-center" rowspan="<?php echo e(count($data['customer_details']) + 1); ?>">
                                                    <?php if($data['payment_status'] == 'paid'): ?>
                                                        <span class="badge bg-success fs-6">مدفوع</span>
                                                    <?php elseif($data['payment_status'] == 'partial'): ?>
                                                        <span class="badge bg-warning fs-6">جزئي</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-danger fs-6">غير مدفوع</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td class="text-center" rowspan="<?php echo e(count($data['customer_details']) + 1); ?>">
                                                    <span class="fw-bold"><?php echo e(number_format($data['remaining_amount'], 2)); ?></span>
                                                    <br><small class="text-muted">جنيه</small>
                                                </td>
                                            </tr>

                                            
                                            <?php $__currentLoopData = $data['customer_details']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $customer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <tr style="background-color: #f8f9fa;">
                                                    <td class="text-center">-</td>
                                                    <td>
                                                        <span class="fw-bold"><?php echo e($customer['name']); ?></span>
                                                        <br><small class="text-muted">عميل مشارك</small>
                                                    </td>
                                                    <td class="text-center">
                                                        <span class="badge bg-light text-dark">مشارك</span>
                                                    </td>
                                                    <td class="text-center">
                                                        <?php
                                                            $customerHours = $customer['hours'];
                                                            $totalHours = floor($customerHours);
                                                            $minutes = round(($customerHours - $totalHours) * 60);
                                                            if ($minutes >= 60) {
                                                                $totalHours += floor($minutes / 60);
                                                                $minutes = $minutes % 60;
                                                            }
                                                            $formattedHours = convertToArabicNumbers($totalHours);
                                                            $formattedMinutes = convertToArabicNumbers($minutes);
                                                        ?>
                                                        <span class="fw-bold"><?php echo e($formattedHours); ?> ساعة و <?php echo e($formattedMinutes); ?> دقيقة</span>
                                                    </td>
                                                    <td class="text-center">
                                                        <?php if(!empty($customer['wells'])): ?>
                                                            <?php $__currentLoopData = $customer['wells']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $well): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                <div class="mb-1">
                                                                    <strong><?php echo e($well['name']); ?>:</strong>
                                                                    <?php
                                                                        $wellHours = $well['hours'];
                                                                        $hours = floor($wellHours);
                                                                        $minutes = round(($wellHours - $hours) * 60);
                                                                        if ($minutes >= 60) {
                                                                            $hours += floor($minutes / 60);
                                                                            $minutes = $minutes % 60;
                                                                        }
                                                                        $formattedHours = convertToArabicNumbers($hours);
                                                                        $formattedMinutes = convertToArabicNumbers($minutes);
                                                                    ?>
                                                                    <?php echo e($formattedHours); ?> ساعة و <?php echo e($formattedMinutes); ?> دقيقة
                                                                    <?php
                                                                        $hourlyRateArabic = app()->getLocale() == 'ae' ?
                                                                            convertToArabicNumbers(number_format($well['hourly_rate'], 2)) :
                                                                            number_format($well['hourly_rate'], 2);
                                                                    ?>
                                                                    <br><small class="text-muted">(<?php echo e($hourlyRateArabic); ?> جنيه/ساعة)</small>
                                                                </div>
                                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td class="text-center">
                                                        <?php
                                                            $customerAmountArabic = app()->getLocale() == 'ae' ?
                                                                convertToArabicNumbers(number_format($customer['amount'], 2)) :
                                                                number_format($customer['amount'], 2);
                                                        ?>
                                                        <span class="fw-bold"><?php echo e($customerAmountArabic); ?></span>
                                                        <br><small class="text-muted">جنيه</small>
                                                    </td>
                                                    
                                                </tr>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار إدارة الأرصدة -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">إدارة الأرصدة السالبة</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <p class="mb-2"><strong>حالة الأرصدة:</strong>
                                                <?php if($balanceStatus): ?>
                                                    <span class="badge bg-success">تم إضافة المبالغ للأرصدة السالبة</span>
                                                <?php else: ?>
                                                    <span class="badge bg-warning">لم يتم إضافة المبالغ للأرصدة بعد</span>
                                                <?php endif; ?>
                                            </p>
                                            <p class="text-muted small">
                                                عند إضافة المبالغ للأرصدة، سيتم إضافة مبلغ سالب لكل عميل وعميل مشارك
                                                ليتم خصمه تلقائياً من الفواتير العادية القادمة.
                                            </p>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="d-flex flex-column gap-2">
                                                <?php if(!$balanceStatus): ?>
                                                    <form action="<?php echo e(route('invoice-reason.add-to-balances', $invoice->id)); ?>" method="POST" style="display: inline;">
                                                        <?php echo csrf_field(); ?>
                                                        <button type="submit" class="btn btn-success btn-sm"
                                                                onclick="return confirm('هل أنت متأكد من إضافة هذه المبالغ للأرصدة السالبة؟')">
                                                            <i class="mdi mdi-plus-circle"></i> إضافة للأرصدة
                                                        </button>
                                                    </form>

                                                    <!-- زر إضافة لفاتورة عادية -->
                                                    <button type="button" class="btn btn-info btn-sm" data-bs-toggle="modal" data-bs-target="#addToInvoiceModal">
                                                        <i class="mdi mdi-file-plus"></i> إضافة لفاتورة عادية
                                                    </button>
                                                <?php else: ?>
                                                    <form action="<?php echo e(route('invoice-reason.remove-from-balances', $invoice->id)); ?>" method="POST" style="display: inline;">
                                                        <?php echo csrf_field(); ?>
                                                        <button type="submit" class="btn btn-danger btn-sm"
                                                                onclick="return confirm('هل أنت متأكد من إلغاء الأرصدة السالبة؟')">
                                                            <i class="mdi mdi-minus-circle"></i> إلغاء من الأرصدة
                                                        </button>
                                                    </form>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Summary Section -->
                    <div class="row justify-content-center text-center mt-5 no-print">
                        <!-- Total Amount -->
                        <div class="col-md-3">
                            <div class="card p-4 border shadow-sm" style="background-color: #e3f2fd;">
                                <h6 class="text-uppercase"><strong>إجمالي المبلغ</strong></h6>
                                <?php
                                    $totalAmountArabic = app()->getLocale() == 'ae' ?
                                        convertToArabicNumbers(number_format($invoice->total_amount_due, 2)) :
                                        number_format($invoice->total_amount_due, 2);
                                ?>
                                <p class="fs-4 mb-0 text-primary fw-bold"><?php echo e($totalAmountArabic); ?> جنيه</p>
                            </div>
                        </div>
                        <!-- Total Hours Worked -->
                        <div class="col-md-3">
                            <div class="card p-4 border shadow-sm" style="background-color: #f3e5f5;">
                                <h6 class="text-uppercase"><strong>إجمالي الساعات</strong></h6>
                                <?php
                                    $totalHours = $totalHoursWorked;
                                    $hours = floor($totalHours);
                                    $minutes = round(($totalHours - $hours) * 60);
                                    if ($minutes >= 60) {
                                        $hours += floor($minutes / 60);
                                        $minutes = $minutes % 60;
                                    }
                                    $formattedHours = convertToArabicNumbers($hours);
                                    $formattedMinutes = convertToArabicNumbers($minutes);
                                ?>
                                <p class="fs-4 mb-0 text-purple fw-bold"><?php echo e($formattedHours); ?> ساعة و <?php echo e($formattedMinutes); ?> دقيقة</p>
                            </div>
                        </div>
                        <!-- Hourly Rate -->
                        <div class="col-md-3">
                            <div class="card p-4 border shadow-sm" style="background-color: #e8f5e8;">
                                <h6 class="text-uppercase"><strong>سعر الساعة</strong></h6>
                                <?php
                                    $hourlyRate = $totalHoursWorked > 0 ? $invoice->total_amount_due / $totalHoursWorked : 0;
                                    $hourlyRateArabic = app()->getLocale() == 'ae' ?
                                        convertToArabicNumbers(number_format($hourlyRate, 2)) :
                                        number_format($hourlyRate, 2);
                                ?>
                                <p class="fs-4 mb-0 text-success fw-bold"><?php echo e($hourlyRateArabic); ?> جنيه</p>
                            </div>
                        </div>
                        <!-- Number of Clients -->
                        <div class="col-md-3">
                            <div class="card p-4 border shadow-sm" style="background-color: #fff3e0;">
                                <h6 class="text-uppercase"><strong>عدد العملاء</strong></h6>
                                <?php
                                    $clientCountArabic = app()->getLocale() == 'ae' ?
                                        convertToArabicNumbers(count($clientData)) :
                                        count($clientData);
                                ?>
                                <p class="fs-4 mb-0 text-warning fw-bold"><?php echo e($clientCountArabic); ?> عميل</p>
                            </div>
                        </div>
                    </div>
                    <div class="hstack gap-2 justify-content-end d-print-none mt-4 action-buttons">
                        <a href="javascript:window.print()" class="btn btn-success"><i class="ri-printer-line align-bottom me-1"></i> <?php echo app('translator')->get('client-invoice.print'); ?></a>
                    </div>
                    <!-- Back Button -->
                    <div class="d-flex justify-content-end mt-4 action-buttons">
                        <a href="<?php echo e(route('invoice-reason.index')); ?>" class="btn btn-secondary">
                            <i class="mdi mdi-arrow-left me-1"></i> <?php echo e(__('showInvoice.back_to_invoices')); ?>

                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal لإضافة المبالغ لفاتورة عادية -->
    <div class="modal fade" id="addToInvoiceModal" tabindex="-1" aria-labelledby="addToInvoiceModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addToInvoiceModalLabel">إضافة المبالغ لفاتورة عادية</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form action="<?php echo e(route('invoice-reason.add-to-regular-invoice', $invoice->id)); ?>" method="POST">
                    <?php echo csrf_field(); ?>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-12 mb-3">
                                <label for="regular_invoice_id" class="form-label">اختر الفاتورة العادية</label>
                                <select name="regular_invoice_id" id="regular_invoice_id" class="form-select" required>
                                    <option value="">-- اختر فاتورة --</option>
                                    <!-- سيتم ملؤها بـ AJAX -->
                                </select>
                            </div>
                            <div class="col-12 mb-3">
                                <div class="alert alert-info">
                                    <h6><strong>ملاحظة مهمة:</strong></h6>
                                    <ul class="mb-0">
                                        <li>سيتم إضافة المبالغ كمبلغ إضافي للعملاء الذين لديهم ساعات في الفاتورة العادية المختارة</li>
                                        <li>العملاء الذين ليس لديهم ساعات سيتم إضافة مبالغهم للرصيد السالب</li>
                                        <li>في الفواتير القادمة، سيتم تطبيق الرصيد السالب تلقائياً عند وجود ساعات للعميل</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="col-12" id="invoice-preview" style="display: none;">
                                <h6>معاينة توزيع المبالغ:</h6>
                                <div id="preview-content"></div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary" id="confirmAddBtn" disabled>
                            <i class="mdi mdi-check"></i> تأكيد الإضافة
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('script'); ?>
    <script src="<?php echo e(URL::asset('/assets/libs/apexcharts/apexcharts.min.js')); ?>"></script>
    <script src="<?php echo e(URL::asset('/assets/js/pages/dashboard-client-statistics-projects.init.js')); ?>"></script>
    <script src="<?php echo e(URL::asset('/assets/js/app.min.js')); ?>"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const modal = document.getElementById('addToInvoiceModal');
            const invoiceSelect = document.getElementById('regular_invoice_id');
            const previewDiv = document.getElementById('invoice-preview');
            const previewContent = document.getElementById('preview-content');
            const confirmBtn = document.getElementById('confirmAddBtn');

            // تحميل الفواتير العادية عند فتح الـ Modal
            modal.addEventListener('show.bs.modal', function() {
                loadRegularInvoices();
            });

            // معاينة التوزيع عند اختيار فاتورة
            invoiceSelect.addEventListener('change', function() {
                if (this.value) {
                    loadInvoicePreview(this.value);
                    confirmBtn.disabled = false;
                } else {
                    previewDiv.style.display = 'none';
                    confirmBtn.disabled = true;
                }
            });

            function loadRegularInvoices() {
                fetch('<?php echo e(route("invoice-reason.get-regular-invoices")); ?>')
                    .then(response => response.json())
                    .then(data => {
                        invoiceSelect.innerHTML = '<option value="">-- اختر فاتورة --</option>';
                        data.invoices.forEach(invoice => {
                            const option = document.createElement('option');
                            option.value = invoice.id;
                            option.textContent = `فاتورة ${invoice.id} - ${invoice.period} (${invoice.total_amount} جنيه)`;
                            invoiceSelect.appendChild(option);
                        });
                    })
                    .catch(error => {
                        console.error('Error loading invoices:', error);
                        alert('حدث خطأ في تحميل الفواتير');
                    });
            }

            function loadInvoicePreview(invoiceId) {
                fetch('<?php echo e(route("invoice-reason.preview-distribution", $invoice->id)); ?>', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                    },
                    body: JSON.stringify({
                        regular_invoice_id: invoiceId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayPreview(data.preview);
                        previewDiv.style.display = 'block';
                    } else {
                        alert('حدث خطأ: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error loading preview:', error);
                    alert('حدث خطأ في تحميل المعاينة');
                });
            }

            function displayPreview(preview) {
                let html = '<div class="table-responsive"><table class="table table-sm table-bordered">';
                html += '<thead><tr><th>العميل</th><th>النوع</th><th>المبلغ</th><th>الوجهة</th></tr></thead><tbody>';

                preview.forEach(item => {
                    html += `<tr>
                        <td>${item.client_name}</td>
                        <td>${item.type}</td>
                        <td>${item.amount.toFixed(2)} جنيه</td>
                        <td><span class="badge ${item.destination === 'additional' ? 'bg-success' : 'bg-warning'}">${item.destination_text}</span></td>
                    </tr>`;
                });

                html += '</tbody></table></div>';
                previewContent.innerHTML = html;
            }
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\laragon\www\Beer-Elshabab\resources\views/invoice-reason/show.blade.php ENDPATH**/ ?>