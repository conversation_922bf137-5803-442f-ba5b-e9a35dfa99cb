@extends('layouts.master')

@section('content')
    @component('components.breadcrumb')
        @slot('li_1')
            @lang('showInvoice.management')
        @endslot
        @slot('title')
            @lang('showInvoice.invoice_details')
        @endslot
    @endcomponent

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-body">
                    <!-- Success and Error Messages -->
                    @if(session('success'))
                        <div class="alert alert-success">{{ session('success') }}</div>
                    @elseif(session('error'))
                        <div class="alert alert-danger">{{ session('error') }}</div>
                    @endif

                    <!-- Invoice Summary -->
                    <div class="invoice-summary mb-4 text-center">
                        <h4 class="card-title mb-3"><strong>@lang('showInvoice.invoice_summary')</strong></h4>
                        <div class="row">
                            <div class="col-md-6">
                                @php
                                    $totalAmountArabic = app()->getLocale() == 'ae' ?
                                        convertToArabicNumbers(number_format($invoice->total_amount_due, 2)) :
                                        number_format($invoice->total_amount_due, 2);
                                @endphp
                                <p class="fs-5"><strong>@lang('showInvoice.total_amount_due') {{ $totalAmountArabic }} {{ __('showInvoice.currency') }}</strong></p>
                            </div>
                            <div class="col-md-6">
                                @php
                                $monthCarbon = \Carbon\Carbon::parse($invoice->month);
                                $formattedMonth = app()->getLocale() == 'ae'
                                    ? $monthCarbon->locale('ar')->translatedFormat('F Y')
                                    : $monthCarbon->format('F Y');
                                @endphp
                                <p class="fs-5"><strong>@lang('showInvoice.month'): {{ $formattedMonth }}</strong></p>
                            </div>

                            @foreach($invoice->wells as $well)
                            <div class="col-md-6">
                                <p class="fs-5"><strong>{{ $well['name'] }}: @formatNumber($well->pivot->hour_price) {{ __('showInvoice.currency') }} </strong></p>
                            </div>
                            @endforeach
                            @php
                                // جمع أسباب المبالغ الإضافية من فواتير الأسباب
                                $additionalReasons = [];
                                if (!empty($separatedPayments)) {
                                    foreach ($separatedPayments as $payment) {
                                        if (!empty($payment['additional_amount_reason'])) {
                                            $reasons = explode(' | ', $payment['additional_amount_reason']);
                                            foreach ($reasons as $reason) {
                                                if (!in_array($reason, $additionalReasons)) {
                                                    $additionalReasons[] = $reason;
                                                }
                                            }
                                        }
                                    }
                                }
                            @endphp
                            @if(!empty($additionalReasons))
                            <div class="col-md-6 text-center">
                                        @foreach($additionalReasons as $reason)
                                            <p class="fs-5"><strong>{{ $reason }}  :سبب المبلغ الإضافي</strong></p>
                                        @endforeach
                            </div>

                            @endif
                        </div>
                    </div>

                    <form method="GET" action="{{ route('invoices.showInvoice', $invoice->id) }}" class="mb-4 no-print filter-section">
                        <div class="row justify-content-center align-items-center text-center">
                            <!-- العنوان والفلتر -->
                            <div class="col-md-6">
                                <label for="filter_status" class="form-label fw-bold text-primary">
                                    @lang('showInvoice.filter')
                                </label>
                                <div class="input-group justify-content-center">
                                    <select
                                        name="filter_status"
                                        id="filter_status"
                                        class="form-select border-primary shadow-sm text-center"
                                        onchange="this.form.submit()">
                                        <option value="">@lang('showInvoice.all')</option>
                                        <option value="paid" {{ request('filter_status') == 'paid' ? 'selected' : '' }}>
                                            مدفوعة
                                        </option>
                                        <option value="unpaid" {{ request('filter_status') == 'unpaid' ? 'selected' : '' }}>
                                            غير مدفوعة
                                        </option>
                                        <option value="with_hours" {{ request('filter_status') == 'with_hours' ? 'selected' : '' }}>
                                            بساعات
                                        </option>
                                        <option value="without_hours" {{ request('filter_status') == 'without_hours' ? 'selected' : '' }}>
                                            بدون ساعات
                                        </option>
                                        <option value="has_additional" {{ request('filter_status') == 'has_additional' ? 'selected' : '' }}>
                                            يوجد لديه مبلغ إضافي
                                        </option>
                                        <option value="no_additional_with_hours" {{ request('filter_status') == 'no_additional_with_hours' ? 'selected' : '' }}>
                                            لا يوجد لديه مبلغ إضافي ولكن لديه ساعات
                                        </option>
                                        <option value="has_due" {{ request('filter_status') == 'has_due' ? 'selected' : '' }}>
                                            @lang('showInvoice.has_due')
                                        </option>
                                        <option value="no_due" {{ request('filter_status') == 'no_due' ? 'selected' : '' }}>
                                            @lang('showInvoice.no_due')
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </form>

                    @if(request('filter_status'))
                        <div class="alert alert-info text-center no-print">
                            <i class="mdi mdi-information me-2"></i>
                            <strong>تم تطبيق فلتر:</strong>
                            @switch(request('filter_status'))
                                @case('paid')
                                    عرض العملاء المدفوعة فواتيرهم فقط
                                    @break
                                @case('unpaid')
                                    عرض العملاء غير المدفوعة فواتيرهم فقط
                                    @break
                                @case('with_hours')
                                    عرض العملاء الذين لديهم ساعات عمل فقط
                                    @break
                                @case('without_hours')
                                    عرض العملاء الذين ليس لديهم ساعات عمل فقط
                                    @break
                                @case('has_additional')
                                    عرض العملاء الذين لديهم مبلغ إضافي فقط
                                    @break
                                @case('no_additional_with_hours')
                                    عرض العملاء الذين ليس لديهم مبلغ إضافي ولكن لديهم ساعات فقط
                                    @break
                                @case('has_due')
                                    عرض العملاء الذين لديهم مبلغ مستحق فقط
                                    @break
                                @case('no_due')
                                    عرض العملاء الذين ليس لديهم مبلغ مستحق فقط
                                    @break
                                @default
                                    فلتر مخصص
                            @endswitch
                            - <a href="{{ route('invoices.showInvoice', $invoice->id) }}" class="text-decoration-none">إزالة الفلتر</a>
                        </div>
                    @endif

                    <!-- Client Breakdown Table -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">@lang('showInvoice.client_breakdown')</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover align-middle table-bordered mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>@lang('showInvoice.id')</th>
                                            <th>@lang('showInvoice.client_name')</th>
                                            <th>@lang('showInvoice.amount_due')</th>
                                            <th>@lang('showInvoice.additional_due')</th>
                                            <th>@lang('showInvoice.maintenance_and_operation_fees')</th>
                                            <th>@lang('showInvoice.payment_status')</th>
                                            <th>@lang('showInvoice.remaining_amount')</th>
                                            <th class="text-center">@lang('showInvoice.hours_worked')</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @if(empty($separatedPayments))
                                            <tr>
                                                <td colspan="8" class="text-center">@lang('showInvoice.no_records')</td>
                                            </tr>
                                        @else
                                            @php
                                                $groupedPayments = [];
                                                foreach($separatedPayments as $payment) {
                                                    $clientId = $payment['client_id'];
                                                    if (!isset($groupedPayments[$clientId])) {
                                                        $groupedPayments[$clientId] = [
                                                            'main' => null,
                                                            'customers' => []
                                                        ];
                                                    }

                                                    if ($payment['type'] == 'main_client') {
                                                        $groupedPayments[$clientId]['main'] = $payment;
                                                    } else {
                                                        $groupedPayments[$clientId]['customers'][] = $payment;
                                                    }
                                                }
                                            @endphp

                                            @foreach($groupedPayments as $clientId => $group)
                                                @if($group['main'])
                                                    <tr>
                                                        <td><strong>{{ $clientId }}</strong></td>
                                                        <td>
                                                            <strong>{{ $group['main']['client_name'] }}</strong>
                                                            @if(!empty($group['customers']))
                                                                <ul style="list-style: none; padding-left: 0; margin: 0;">
                                                                    @foreach($group['customers'] as $customer)
                                                                        <li><strong>{{ $customer['customer_name'] }}</strong></li>
                                                                    @endforeach
                                                                </ul>
                                                            @endif
                                                        </td>
                                                        <td>
                                                            @php
                                                                $amountDueArabic = app()->getLocale() == 'ae' ?
                                                                    convertToArabicNumbers(number_format($group['main']['amount_due'], 2)) :
                                                                    number_format($group['main']['amount_due'], 2);
                                                            @endphp
                                                            <strong>{{ $amountDueArabic }} {{ __('showInvoice.currency') }}</strong>
                                                            @if(!empty($group['customers']))
                                                                <ul style="list-style: none; padding-left: 0; margin: 0;">
                                                                    @foreach($group['customers'] as $customer)
                                                                        @php
                                                                            $customerAmountArabic = app()->getLocale() == 'ae' ?
                                                                                convertToArabicNumbers(number_format($customer['amount_due'], 2)) :
                                                                                number_format($customer['amount_due'], 2);
                                                                        @endphp
                                                                        <li><strong>{{ $customerAmountArabic }} {{ __('showInvoice.currency') }}</strong></li>
                                                                    @endforeach
                                                                </ul>
                                                            @endif
                                                        </td>
                                                        <td>
                                                            @php
                                                                $additionalPaidArabic = app()->getLocale() == 'ae' ?
                                                                    convertToArabicNumbers(number_format($group['main']['additional_paid'], 2)) :
                                                                    number_format($group['main']['additional_paid'], 2);
                                                            @endphp
                                                            <strong>{{ $additionalPaidArabic }} {{ __('showInvoice.currency') }}</strong>
                                                            @if(!empty($group['customers']))
                                                                <ul style="list-style: none; padding-left: 0; margin: 0;">
                                                                    @foreach($group['customers'] as $customer)
                                                                        @php
                                                                            $customerAdditionalArabic = app()->getLocale() == 'ae' ?
                                                                                convertToArabicNumbers(number_format($customer['additional_paid'], 2)) :
                                                                                number_format($customer['additional_paid'], 2);
                                                                        @endphp
                                                                        <li><strong>{{ $customerAdditionalArabic }} {{ __('showInvoice.currency') }}</strong></li>
                                                                    @endforeach
                                                                </ul>
                                                            @endif
                                                        </td>
                                                        <td class="text-center">
                                                            @php
                                                                $activeBonusArabic = app()->getLocale() == 'ae' ?
                                                                    convertToArabicNumbers(number_format($group['main']['active_bonus'], 2)) :
                                                                    number_format($group['main']['active_bonus'], 2);
                                                            @endphp
                                                            <strong>{{ $activeBonusArabic }} {{ __('showInvoice.currency') }}</strong>
                                                            @if(!empty($group['customers']))
                                                                <ul style="list-style: none; padding-left: 0; margin: 0;">
                                                                    @foreach($group['customers'] as $customer)
                                                                        @php
                                                                            $customerBonusArabic = app()->getLocale() == 'ae' ?
                                                                                convertToArabicNumbers(number_format($customer['active_bonus'], 2)) :
                                                                                number_format($customer['active_bonus'], 2);
                                                                        @endphp
                                                                        <li><strong>{{ $customerBonusArabic }} {{ __('showInvoice.currency') }}</strong></li>
                                                                    @endforeach
                                                                </ul>
                                                            @endif
                                                        </td>
                                                        <td><strong>
                                                            @if($group['main']['payment_status'] == 'paid')
                                                                <span class="badge bg-success">@lang('showInvoice.paid')</span>
                                                            @elseif($group['main']['payment_status'] == 'partial')
                                                                <span class="badge bg-warning">@lang('showInvoice.partial_payment')</span>
                                                            @else
                                                                <span class="badge bg-danger">@lang('showInvoice.unpaid')</span>
                                                            @endif
                                                        </strong></td>
                                                        <td>
                                                            @php
                                                                $remainingAmountArabic = app()->getLocale() == 'ae' ?
                                                                    convertToArabicNumbers(number_format($group['main']['remaining_amount'], 2)) :
                                                                    number_format($group['main']['remaining_amount'], 2);
                                                            @endphp
                                                            <strong>{{ $remainingAmountArabic }} {{ __('showInvoice.currency') }}</strong>
                                                            @if(!empty($group['customers']))
                                                                <ul style="list-style: none; padding-left: 0; margin: 0;">
                                                                    @foreach($group['customers'] as $customer)
                                                                        @php
                                                                            $customerRemainingArabic = app()->getLocale() == 'ae' ?
                                                                                convertToArabicNumbers(number_format($customer['remaining_amount'], 2)) :
                                                                                number_format($customer['remaining_amount'], 2);
                                                                        @endphp
                                                                        <li><strong>{{ $customerRemainingArabic }} {{ __('showInvoice.currency') }}</strong></li>
                                                                    @endforeach
                                                                </ul>
                                                            @endif
                                                        </td>
                                                        <td>
                                                            @if(isset($combinedWorkData[$clientId]))
                                                                @php $data = $combinedWorkData[$clientId]; @endphp
                                                                @if($data['client_hours'] > 0)
                                                                    @foreach($data['client_wells'] as $well)
                                                                        @php
                                                                            $totalHoursWorked = $well['hours'];
                                                                            $totalHours = floor($totalHoursWorked);
                                                                            $minutes = round(($totalHoursWorked - $totalHours) * 60);
                                                                            if ($minutes >= 60) {
                                                                                $totalHours += floor($minutes / 60);
                                                                                $minutes = $minutes % 60;
                                                                            }
                                                                            $locale = app()->getLocale();
                                                                            if ($locale === 'ae') {
                                                                                $formattedHours = convertToArabicNumbers($totalHours);
                                                                                $formattedMinutes = convertToArabicNumbers($minutes);
                                                                                $hoursLabel = trans('hours.hours');
                                                                                $minutesLabel = trans('hours.minutes');
                                                                            } else {
                                                                                $formattedHours = $totalHours;
                                                                                $formattedMinutes = $minutes;
                                                                                $hoursLabel = trans('hours.hours');
                                                                                $minutesLabel = trans('hours.minutes');
                                                                            }
                                                                        @endphp
                                                                        <div><strong>{{ $well['name'] }}: {{ $formattedHours }} {{ $hoursLabel }}, {{ $formattedMinutes }} {{ $minutesLabel }}</strong></div>
                                                                    @endforeach
                                                                @endif

                                                                @if(!empty($group['customers']) && $data['customer_hours'] > 0)
                                                                    @foreach($data['customer_data'] as $customer)
                                                                        @if($customer['hours'] > 0)
                                                                            @foreach($customer['wells'] as $well)
                                                                                @php
                                                                                    $totalHoursWorked = $well['hours'];
                                                                                    $totalHours = floor($totalHoursWorked);
                                                                                    $minutes = round(($totalHoursWorked - $totalHours) * 60);
                                                                                    if ($minutes >= 60) {
                                                                                        $totalHours += floor($minutes / 60);
                                                                                        $minutes = $minutes % 60;
                                                                                    }
                                                                                    $locale = app()->getLocale();
                                                                                    if ($locale === 'ae') {
                                                                                        $formattedHours = convertToArabicNumbers($totalHours);
                                                                                        $formattedMinutes = convertToArabicNumbers($minutes);
                                                                                        $hoursLabel = trans('hours.hours');
                                                                                        $minutesLabel = trans('hours.minutes');
                                                                                    } else {
                                                                                        $formattedHours = $totalHours;
                                                                                        $formattedMinutes = $minutes;
                                                                                        $hoursLabel = trans('hours.hours');
                                                                                        $minutesLabel = trans('hours.minutes');
                                                                                    }
                                                                                @endphp
                                                                                <div><strong>{{ $customer['name'] }} - {{ $well['name'] }}: {{ $formattedHours }} {{ $hoursLabel }}, {{ $formattedMinutes }} {{ $minutesLabel }}</strong></div>
                                                                            @endforeach
                                                                        @endif
                                                                    @endforeach
                                                                @endif
                                                            @endif
                                                        </td>
                                                    </tr>
                                                @endif
                                            @endforeach
                                        @endif
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>



                    <!-- Total Remaining Amount Box -->
                    <div class="card mt-4">
                        <div class="card-body text-center">
                            <h5 class="card-title mb-3">
                                @if(request('filter_status'))
                                    إجمالي المبلغ المتبقي (مفلتر)
                                @else
                                    @lang('showInvoice.total_remaining_amount')
                                @endif
                            </h5>
                            @php
                                $totalRemaining = 0;
                                $totalAmountDue = 0;
                                $totalAdditionalPaid = 0;
                                $totalActiveBonus = 0;

                                if (!empty($separatedPayments)) {
                                    foreach ($separatedPayments as $payment) {
                                        $totalRemaining += $payment['remaining_amount'];
                                        $totalAmountDue += $payment['amount_due'];
                                        $totalAdditionalPaid += $payment['additional_paid'];
                                        $totalActiveBonus += $payment['active_bonus'];
                                    }
                                }
                            @endphp
                            <div class="row">
                                <div class="col-md-6">
                                    @php
                                        $totalRemainingArabic = app()->getLocale() == 'ae' ?
                                            convertToArabicNumbers(number_format($totalRemaining, 2)) :
                                            number_format($totalRemaining, 2);
                                    @endphp
                                    <p class="fs-5"><strong>المبلغ المتبقي: {{ $totalRemainingArabic }} {{ __('showInvoice.currency') }}</strong></p>
                                </div>
                                <div class="col-md-6">
                                    @php
                                        $totalAmountDueArabic = app()->getLocale() == 'ae' ?
                                            convertToArabicNumbers(number_format($totalAmountDue, 2)) :
                                            number_format($totalAmountDue, 2);
                                    @endphp
                                    <p class="fs-5"><strong>إجمالي الساعات: {{ $totalAmountDueArabic }} {{ __('showInvoice.currency') }}</strong></p>
                                </div>
                                @if($totalAdditionalPaid > 0)
                                <div class="col-md-6">
                                    @php
                                        $totalAdditionalPaidArabic = app()->getLocale() == 'ae' ?
                                            convertToArabicNumbers(number_format($totalAdditionalPaid, 2)) :
                                            number_format($totalAdditionalPaid, 2);
                                    @endphp
                                    <p class="fs-5"><strong>إجمالي المبلغ الإضافي: {{ $totalAdditionalPaidArabic }} {{ __('showInvoice.currency') }}</strong></p>
                                </div>
                                @endif
                                @if($totalActiveBonus > 0)
                                <div class="col-md-6">
                                    @php
                                        $totalActiveBonusArabic = app()->getLocale() == 'ae' ?
                                            convertToArabicNumbers(number_format($totalActiveBonus, 2)) :
                                            number_format($totalActiveBonus, 2);
                                    @endphp
                                    <p class="fs-5"><strong>إجمالي رسوم الصيانة: {{ $totalActiveBonusArabic }} {{ __('showInvoice.currency') }}</strong></p>
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <div class="hstack gap-2 justify-content-end d-print-none mt-4 no-print">
                        <a href="javascript:window.print()" class="btn btn-success"><i class="ri-printer-line align-bottom me-1"></i> @lang('client-invoice.print')</a>
                    </div>
                    <!-- Back Button -->
                    <div class="d-flex justify-content-end mt-4 no-print">
                        <a href="{{ route('invoices.index') }}" class="btn btn-secondary">
                            <i class="mdi mdi-arrow-left me-1"></i> @lang('showInvoice.back_to_invoices')
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('script')
    <script src="{{ URL::asset('/assets/libs/apexcharts/apexcharts.min.js') }}"></script>
    <script src="{{ URL::asset('/assets/js/pages/dashboard-client-statistics-projects.init.js') }}"></script>
    <script src="{{ URL::asset('/assets/js/app.min.js') }}"></script>
@endsection

@section('css')
<style>
@media print {
    .no-print {
        display: none !important;
    }

    .filter-section {
        display: none !important;
    }

    .alert {
        display: none !important;
    }

    .btn {
        display: none !important;
    }

    .card-body .row .col-md-6 p {
        font-size: 12px !important;
    }

    /* تحسين الطباعة */
    body {
        font-family: 'Arial', sans-serif !important;
        font-weight: bold !important;
        line-height: 1.6 !important;
    }

    .table {
        font-weight: bold !important;
        border-collapse: separate !important;
        border-spacing: 0 8px !important;
    }

    .table th,
    .table td {
        font-weight: bold !important;
        padding: 12px 8px !important;
        border: 2px solid #000 !important;
        background-color: #fff !important;
    }

    .table th {
        background-color: #f8f9fa !important;
        font-size: 14px !important;
    }

    .table td {
        font-size: 13px !important;
    }

    /* مسافات بين الصفوف */
    .table tbody tr {
        margin-bottom: 10px !important;
    }

    /* تحسين العناوين */
    h4, h5, h6 {
        font-weight: bold !important;
        color: #000 !important;
    }

    .fs-5 {
        font-size: 16px !important;
        font-weight: bold !important;
    }
}
</style>
@endsection
